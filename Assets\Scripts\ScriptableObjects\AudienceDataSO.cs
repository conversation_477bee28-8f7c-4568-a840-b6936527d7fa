using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 听众数据 ScriptableObject
/// 存储单个听众的基础数据
/// </summary>
[CreateAssetMenu(fileName = "AudienceData", menuName = "Midnight Broadcasting/Audience Data")]
public class AudienceDataSO : ScriptableObject
{
    [Header("=== 基础信息 ===")]
    [Tooltip("听众名称")]
    public string audienceName;
    
    [Tooltip("听众描述")]
    [TextArea(3, 5)]
    public string description;
    
    [Header("=== 信任度系统 ===")]
    [Tooltip("当前深度信任值 (DTV)")]
    [Range(0, 5)]
    public int currentDTV = 0;
    
    [Tooltip("当前听众状态")]
    public AudienceState currentState = AudienceState.Unknown;
    
    [Header("=== 关键词系统 ===")]
    [Tooltip("已收集的关键词")]
    public List<string> collectedKeywords = new List<string>();
    
    [Header("=== 故事进度 ===")]
    [Tooltip("已解锁的故事块等级")]
    public bool[] unlockedStoryBlocks = new bool[6]; // Level 1-6
}
