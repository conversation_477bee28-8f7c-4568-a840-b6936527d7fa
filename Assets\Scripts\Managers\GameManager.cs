using UnityEngine;

/// <summary>
/// 游戏管理器
/// 负责游戏的整体流程控制
/// </summary>
public class GameManager : MonoBehaviour
{
    [Header("=== 管理器引用 ===")]
    [Tooltip("游戏数据管理器")]
    public GameDataManager gameDataManager;

    [Tooltip("听众管理器")]
    public AudienceManager audienceManager;

    [Tooltip("故事管理器")]
    public StoryManager storyManager;

    [Tooltip("对话管理器")]
    public DialogueManager dialogueManager;

    [Tooltip("记忆管理器")]
    public MemoryManager memoryManager;

    // 单例模式
    public static GameManager Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeServices();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        StartGame();
    }

    #endregion

    #region 初始化

    /// <summary>
    /// 初始化管理器
    /// </summary>
    private void InitializeServices()
    {
        // 确保管理器组件存在
        if (gameDataManager == null)
            gameDataManager = FindObjectOfType<GameDataManager>();

        if (audienceManager == null)
            audienceManager = FindObjectOfType<AudienceManager>();

        if (storyManager == null)
            storyManager = FindObjectOfType<StoryManager>();

        if (dialogueManager == null)
            dialogueManager = FindObjectOfType<DialogueManager>();

        if (memoryManager == null)
            memoryManager = FindObjectOfType<MemoryManager>();
    }

    #endregion

    #region 游戏流程

    /// <summary>
    /// 开始游戏
    /// </summary>
    private void StartGame()
    {
        Debug.Log("游戏开始");

        // 设置初始游戏阶段
        if (gameDataManager?.coreGameData != null)
        {
            gameDataManager.coreGameData.currentPhase = GamePhase.Preparation;
        }
    }

    /// <summary>
    /// 切换游戏阶段
    /// </summary>
    public void ChangeGamePhase(GamePhase newPhase)
    {
        if (gameDataManager?.coreGameData != null)
        {
            gameDataManager.coreGameData.currentPhase = newPhase;
            Debug.Log($"游戏阶段切换到: {newPhase}");
        }
    }

    #endregion
}
