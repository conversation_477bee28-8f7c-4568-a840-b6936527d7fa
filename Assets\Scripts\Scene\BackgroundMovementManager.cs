using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 场景移动管理器 - 简化版本
/// 直接从 CarController 获取速度进行场景移动
/// </summary>
public class BackgroundMovementManager : MonoBehaviour
{
    [Header("移动控制")]
    [SerializeField] private float m_baseMovementSpeed = 2f;
    [SerializeField] private bool m_isMoving = false;

    [Header("视差层设置")]
    [SerializeField] private List<MovementLayer> m_movementLayers = new List<MovementLayer>();

    [Header("组件引用")]
    [SerializeField] private CarController m_carController;

    [System.Serializable]
    public class MovementLayer
    {
        public Transform transform;
        public float speedMultiplier = 1f;
        public bool isLooping = true;
        public float resetPositionX = -10f;    // 循环重置位置的 X 坐标
        public float startPositionX = 10f;     // 循环开始位置的 X 坐标
    }

    void Start()
    {
        if (m_carController == null)
        {
            m_carController = FindObjectOfType<CarController>();
        }
    }

    void Update()
    {
        if (m_isMoving)
        {
            MoveAllLayers();
        }
    }

    /// <summary>
    /// 移动所有图层 - 直接使用 CarController 的速度
    /// </summary>
    private void MoveAllLayers()
    {
        if (m_carController == null) return;

        // 直接使用 CarController 的当前速度
        float effectiveSpeed = m_carController.m_currentSpeed;
        float deltaMovement = effectiveSpeed * Time.deltaTime;

        foreach (var layer in m_movementLayers)
        {
            if (layer.transform != null)
            {
                // 计算该层的移动距离
                float layerMovement = deltaMovement * layer.speedMultiplier;

                // 向左移动（模拟车辆向右行驶）
                layer.transform.position += Vector3.left * layerMovement;

                // 循环处理
                if (layer.isLooping)
                {
                    HandleLooping(layer);
                }
            }
        }
    }

    /// <summary>
    /// 处理图层循环
    /// </summary>
    private void HandleLooping(MovementLayer layer)
    {
        if (layer.transform.position.x <= layer.resetPositionX)
        {
            Vector3 pos = layer.transform.position;
            pos.x = layer.startPositionX;
            layer.transform.position = pos;
        }
    }

    // 公共接口（由 CarController 的 UnityEvent 调用）
    public void StartMoving() => m_isMoving = true;
    public void StopMoving() => m_isMoving = false;
}