%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 935899b62f48ae5498594680ed17d133, type: 3}
  m_Name: Dialogue Database  test OnUse
  m_EditorClassIdentifier: 
  version: 
  author: 
  description: 
  globalUserScript: 
  emphasisSettings:
  - color: {r: 1, g: 0.25471687, b: 0.25471687, a: 1}
    bold: 0
    italic: 1
    underline: 0
  - color: {r: 1, g: 0, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 1, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 0, b: 1, a: 1}
    bold: 0
    italic: 0
    underline: 0
  baseID: 1
  actors:
  - id: 1
    fields:
    - title: Name
      value: Player
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Display Name
      value: Mr.Smith
      type: 0
      typeString: 
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 2
    fields:
    - title: Name
      value: Detective
      type: 0
      typeString: 
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 3
    fields:
    - title: Name
      value: NPC
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 4
    fields:
    - title: Name
      value: NPC2
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 5
    fields:
    - title: Name
      value: NPC3
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  items: []
  locations:
  - id: 1
    fields:
    - title: Name
      value: 1
      type: 0
      typeString: CustomFieldType_Location
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
  variables:
  - id: 1
    fields:
    - title: Name
      value: Alert
      type: 0
      typeString: 
    - title: Initial Value
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: 
      type: 0
      typeString: 
  - id: 2
    fields:
    - title: Name
      value: AtWork
      type: 0
      typeString: 
    - title: Initial Value
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Description
      value: 
      type: 0
      typeString: 
  - id: 3
    fields:
    - title: Name
      value: IsArrest
      type: 0
      typeString: 
    - title: Initial Value
      value: 0
      type: 1
      typeString: CustomFieldType_Number
    - title: Description
      value: 
      type: 0
      typeString: 
  - id: 4
    fields:
    - title: Name
      value: FinishTalking
      type: 0
      typeString: 
    - title: Initial Value
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Description
      value: 
      type: 0
      typeString: 
  conversations:
  - id: 1
    fields:
    - title: Title
      value: Questioning
      type: 0
      typeString: CustomFieldType_Text
    - title: Description
      value: "\u8FD9\u4E2A\u5BF9\u8BDD\u53EF\u4EE5\u4F7F\u7528\u4E00\u4E2ATrigger\u5B9E\u73B0\u7ED3\u675F\u5BF9\u8BDD\u540E\u6309\u5206\u652F\u91CD\u590D\u6700\u540E\u4E00\u53E5"
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 5
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 15
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2058.104
        y: 296.9071
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6709\u7A7A\u5417\uFF0C [var=Actor]\uFF1F"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 'AnimatorPlayWait(InteractionPull,Listener,0.5) '
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 1
        destinationConversationID: 1
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: Variable["FinishTalking"] == false
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2061.2412
        y: 344.17065
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: Questioning
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F60\u6628\u665A9\u70B9\u5728\u54EA\u513F\uFF1F"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 3
        destinationConversationID: 1
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 3
        destinationConversationID: 1
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2056.9277
        y: 397.28363
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5728\u5BB6\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 4
        destinationConversationID: 1
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: Variable["AtWork"] = false
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1911.2678
        y: 453.27075
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5728\u5DE5\u4F5C"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 5
        destinationConversationID: 1
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: Variable["AtWork"] = true;
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2214.528
        y: 448.02155
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "Lacy Devine \u5728\u4F60\u7684\u5E97\u91CC\u88AB\u53D1\u73B0\u6B7B\u4EA1\uFF0C\u6B7B\u4EA1\u65F6\u95F4\u662F\u665A\u4E0A9\u70B9\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 6
        destinationConversationID: 1
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2067.4084
        y: 514.5071
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u5728\u5BB6\u91CC\u7761\u89C9\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 7
        destinationConversationID: 1
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 7
        destinationConversationID: 1
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2068.5042
        y: 564.5071
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u67E5\u8BC1\u5C5E\u5B9E\u3002\u4F60\u53EF\u4EE5\u8D70\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 8
        destinationConversationID: 1
        destinationDialogueID: 12
        isConnector: 0
        priority: 2
      conditionsString: Variable["AtWork"] == false
      userScript: Variable["IsArrest"] = 1
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1877.8529
        y: 670.8303
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F60\u4E0D\u662F\u8BF4\u4F60\u5728\u4E0A\u73ED\u5417\uFF1F\u4F60\u88AB\u902E\u6355\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 9
        destinationConversationID: 1
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 9
        destinationConversationID: 1
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: Variable["AtWork"] == true
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2208.5046
        y: 665.3063
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u9700\u8981\u5F8B\u5E08"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 10
        destinationConversationID: 1
        destinationDialogueID: 14
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: Variable["IsArrest"] = 2
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2101.2263
        y: 775.93866
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: "[em1][\u9003\u8DD1.][/em1]"
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u62DC\u62DC\u4E86\u60A8\u5185!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 11
        destinationConversationID: 1
        destinationDialogueID: 13
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: Variable["IsArrest"] = 3
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2313.7517
        y: 783.2042
        width: 160
        height: 30
    - id: 12
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u8C22\u8C22\u914D\u5408"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["IsArrest"] == 1
      userScript: Variable["FinishTalking"] = true
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1831.1042
        y: 841.49927
        width: 160
        height: 30
    - id: 13
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F60\u9003\u4E0D\u6389\u7684"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["IsArrest"] == 3
      userScript: Variable["FinishTalking"] = true
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2371.2717
        y: 918.0777
        width: 160
        height: 30
    - id: 14
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F60\u88AB\u6355\u4E86"
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: Variable["IsArrest"] == 2
      userScript: Variable["FinishTalking"] = true
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2101.2263
        y: 920.18524
        width: 160
        height: 30
    - id: 15
      fields:
      - title: Title
        value: "\u7B5B\u9009\u5668"
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: "\u8FD9\u662F\u4E00\u4E2A\u7B5B\u9009\u5668\uFF0CFinishTalking==ture\u65F6\u7ECF\u8FC7\u5B83\uFF0C\u7136\u540E\u6309\u7167IsArrest\u9009\u62E9\u5206\u652F"
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 5
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 1
      isRoot: 0
      isGroup: 1
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 13
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 14
        isConnector: 0
        priority: 2
      - originConversationID: 1
        originDialogueID: 15
        destinationConversationID: 1
        destinationDialogueID: 12
        isConnector: 0
        priority: 2
      conditionsString: Variable["FinishTalking"] == true
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 2641.118
        y: 546.35913
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 1314.1597, y: 230.07687}
    canvasZoom: 0.9400001
  - id: 2
    fields:
    - title: Title
      value: New Conversation 2
      type: 0
      typeString: 
    - title: Description
      value: 
      type: 0
      typeString: 
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 212
        y: 169.20001
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Hello
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Priority
        value: 1
        type: 1
        typeString: CustomFieldType_Number
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 522.6224
        y: 114.2
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Nice weather, eh?
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Priority
        value: 5
        type: 1
        typeString: CustomFieldType_Number
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 525.3965
        y: 188.48566
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: How about them sports?
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Priority
        value: 0
        type: 1
        typeString: CustomFieldType_Number
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 522.58435
        y: 256.52386
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1
  - id: 3
    fields:
    - title: Title
      value: Quest Conversation 1
      type: 0
      typeString: 
    - title: Description
      value: The NPC is a quest giver in a video game. The NPC offers the player
        a quest. This conversation relates to the quest.
      type: 0
      typeString: 
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: 
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 0
        destinationConversationID: 3
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 3
        originDialogueID: 0
        destinationConversationID: 3
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      - originConversationID: 3
        originDialogueID: 0
        destinationConversationID: 3
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 496
        y: 50
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: NPC offers quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: Hello, [var=Actor].
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 1
        destinationConversationID: 3
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      - originConversationID: 3
        originDialogueID: 1
        destinationConversationID: 3
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 184
        y: 134
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: NPC asks about status of active quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 2
        destinationConversationID: 3
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      - originConversationID: 3
        originDialogueID: 2
        destinationConversationID: 3
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 600
        y: 134
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: NPC thanks player for having previously completed quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 3
        destinationConversationID: 3
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 912
        y: 134
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: Player accepts quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 80
        y: 218
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: Player declines quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 288
        y: 218
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: Player says they are still working on quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 496
        y: 218
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: Player turns in completed quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 7
        destinationConversationID: 3
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 704
        y: 218
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: NPC thanks player for completing quest.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 8
        destinationConversationID: 3
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 704
        y: 268
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: Player says goodbye.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 704
        y: 318
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Title
        value: Player says goodbye.
        type: 0
        typeString: CustomFieldType_Text
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 912
        y: 184
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 103.20001, y: 0}
    canvasZoom: 1
  syncInfo:
    syncActors: 0
    syncItems: 0
    syncLocations: 0
    syncVariables: 0
    syncActorsDatabase: {fileID: 0}
    syncItemsDatabase: {fileID: 0}
    syncLocationsDatabase: {fileID: 0}
    syncVariablesDatabase: {fileID: 0}
  templateJson: '{"treatItemsAsQuests":true,"actorFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"IsPlayer","value":"False","type":2,"typeString":"CustomFieldType_Boolean"}],"itemFields":[{"title":"Name","value":"","type":0,"typeString":""},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":""},{"title":"Is
    Item","value":"True","type":2,"typeString":"CustomFieldType_Boolean"}],"questFields":[{"title":"Name","value":"","type":0,"typeString":""},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":""},{"title":"Success
    Description","value":"","type":0,"typeString":""},{"title":"Failure Description","value":"","type":0,"typeString":""},{"title":"State","value":"unassigned","type":0,"typeString":""},{"title":"Is
    Item","value":"False","type":2,"typeString":"CustomFieldType_Boolean"}],"locationFields":[{"title":"Name","value":"","type":0,"typeString":""},{"title":"Description","value":"","type":0,"typeString":""}],"variableFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Initial
    Value","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"}],"conversationFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"0","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"0","type":5,"typeString":"CustomFieldType_Actor"}],"dialogueEntryFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Menu
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Dialogue
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Sequence","value":"","type":0,"typeString":"CustomFieldType_Text"}],"actorPrimaryFieldTitles":[],"itemPrimaryFieldTitles":[],"questPrimaryFieldTitles":[],"locationPrimaryFieldTitles":[],"variablePrimaryFieldTitles":[],"conversationPrimaryFieldTitles":[],"dialogueEntryPrimaryFieldTitles":[],"npcLineColor":{"r":1.0,"g":0.0,"b":0.0,"a":1.0},"pcLineColor":{"r":0.0,"g":0.0,"b":1.0,"a":1.0},"repeatLineColor":{"r":0.5,"g":0.5,"b":0.5,"a":1.0}}'
