using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 主要保存数据结构
/// </summary>
[System.Serializable]
public class SaveData
{
    public SerializableCoreGameData coreGameData;
    public List<SerializableAudienceData> audienceDataList;
    public List<SerializableMemoryData> memoryDataList;
    public string saveVersion;
    public string saveTime;
}

/// <summary>
/// 可序列化的核心游戏数据
/// </summary>
[System.Serializable]
public class SerializableCoreGameData
{
    public float currentStamina;
    public float currentWarmth;
    public int totalFans;
    public float money;
    public int currentNight;
    public GamePhase currentPhase;
    public string lastPlayTimeString;
    public bool[] unlockedFeatures;
    public string[] activeNegativeStates;
}

/// <summary>
/// 可序列化的听众数据
/// </summary>
[System.Serializable]
public class SerializableAudienceData
{
    public string audienceId;
    public int currentDTV;
    public bool[] unlockedStoryBlocks;
    public AudienceState currentState;
    public string lastInteractionTimeString;
    public int totalInteractions;
    public int successfulResonances;
    public int failedInquiries;
    public string[] collectedKeywordNames;
    public string[] expiredKeywordNames;
    public string[] audienceTags;
}

/// <summary>
/// 可序列化的回忆数据
/// </summary>
[System.Serializable]
public class SerializableMemoryData
{
    public string memoryName;
    public bool isUnlocked;
    public bool isActive;
    public bool isSuppressed;
    public string unlockTimeString;
}

/// <summary>
/// 存档槽位信息
/// </summary>
[System.Serializable]
public class SaveSlotInfo
{
    public string slotName;
    public string fileName;
    public DateTime saveTime;
    public long fileSize;
    public string description;

    /// <summary>
    /// 获取文件大小的友好显示
    /// </summary>
    public string GetFileSizeString()
    {
        if (fileSize < 1024)
            return $"{fileSize} B";
        else if (fileSize < 1024 * 1024)
            return $"{fileSize / 1024:F1} KB";
        else
            return $"{fileSize / (1024 * 1024):F1} MB";
    }

    /// <summary>
    /// 获取保存时间的友好显示
    /// </summary>
    public string GetSaveTimeString()
    {
        TimeSpan timeDiff = DateTime.Now - saveTime;

        if (timeDiff.TotalMinutes < 1)
            return "刚刚";
        else if (timeDiff.TotalHours < 1)
            return $"{(int)timeDiff.TotalMinutes}分钟前";
        else if (timeDiff.TotalDays < 1)
            return $"{(int)timeDiff.TotalHours}小时前";
        else if (timeDiff.TotalDays < 7)
            return $"{(int)timeDiff.TotalDays}天前";
        else
            return saveTime.ToString("yyyy/MM/dd HH:mm");
    }
}

/// <summary>
/// 保存操作结果
/// </summary>
[System.Serializable]
public class SaveResult
{
    public bool success;
    public string message;
    public string fileName;
    public DateTime saveTime;

    public SaveResult(bool success, string message, string fileName = "")
    {
        this.success = success;
        this.message = message;
        this.fileName = fileName;
        this.saveTime = DateTime.Now;
    }
}

/// <summary>
/// 加载操作结果
/// </summary>
[System.Serializable]
public class LoadResult
{
    public bool success;
    public string message;
    public string fileName;
    public DateTime loadTime;
    public SaveData loadedData;

    public LoadResult(bool success, string message, string fileName = "", SaveData data = null)
    {
        this.success = success;
        this.message = message;
        this.fileName = fileName;
        this.loadTime = DateTime.Now;
        this.loadedData = data;
    }
}

/// <summary>
/// 存档验证结果
/// </summary>
[System.Serializable]
public class SaveValidationResult
{
    public bool isValid;
    public List<string> errors;
    public List<string> warnings;
    public string version;

    public SaveValidationResult()
    {
        errors = new List<string>();
        warnings = new List<string>();
    }

    public void AddError(string error)
    {
        errors.Add(error);
        isValid = false;
    }

    public void AddWarning(string warning)
    {
        warnings.Add(warning);
    }

    public bool HasIssues => errors.Count > 0 || warnings.Count > 0;
}

/// <summary>
/// 存档统计信息
/// </summary>
[System.Serializable]
public class SaveStatistics
{
    public int totalSaves;
    public int totalLoads;
    public int autoSaves;
    public int manualSaves;
    public int backups;
    public DateTime firstSaveTime;
    public DateTime lastSaveTime;
    public long totalSaveSize;

    public void RecordSave(bool isAutoSave, long fileSize)
    {
        totalSaves++;
        if (isAutoSave)
            autoSaves++;
        else
            manualSaves++;

        lastSaveTime = DateTime.Now;
        if (firstSaveTime == default(DateTime))
            firstSaveTime = DateTime.Now;

        totalSaveSize += fileSize;
    }

    public void RecordLoad()
    {
        totalLoads++;
    }

    public void RecordBackup(long fileSize)
    {
        backups++;
        totalSaveSize += fileSize;
    }
}

/// <summary>
/// 存档配置
/// </summary>
[System.Serializable]
public class SaveConfiguration
{
    [Header("=== 基础设置 ===")]
    public string saveFilePrefix = "MidnightBroadcasting";
    public bool enableEncryption = true;
    public bool enableCompression = true;

    [Header("=== 自动保存 ===")]
    public bool enableAutoSave = true;
    public float autoSaveInterval = 300f; // 5分钟
    public int maxAutoSaves = 3;

    [Header("=== 备份设置 ===")]
    public bool enableBackup = true;
    public int maxBackupCount = 5;
    public bool backupOnLoad = true;

    [Header("=== 验证设置 ===")]
    public bool enableValidation = true;
    public bool strictValidation = false;

    [Header("=== 调试设置 ===")]
    public bool enableDebugLog = true;
    public bool enableDetailedLog = false;
}

/// <summary>
/// 存档事件参数
/// </summary>
[System.Serializable]
public class SaveEventArgs
{
    public string slotName;
    public string fileName;
    public bool isAutoSave;
    public bool success;
    public string message;
    public DateTime timestamp;

    public SaveEventArgs(string slotName, string fileName, bool isAutoSave, bool success, string message)
    {
        this.slotName = slotName;
        this.fileName = fileName;
        this.isAutoSave = isAutoSave;
        this.success = success;
        this.message = message;
        this.timestamp = DateTime.Now;
    }
}

/// <summary>
/// 加载事件参数
/// </summary>
[System.Serializable]
public class LoadEventArgs
{
    public string slotName;
    public string fileName;
    public bool success;
    public string message;
    public DateTime timestamp;

    public LoadEventArgs(string slotName, string fileName, bool success, string message)
    {
        this.slotName = slotName;
        this.fileName = fileName;
        this.success = success;
        this.message = message;
        this.timestamp = DateTime.Now;
    }
}

/// <summary>
/// 版本检查结果
/// </summary>
[System.Serializable]
public class SaveVersionCheckResult
{
    public string saveVersion;
    public string currentVersion;
    public bool isCompatible;
    public bool needsMigration;
    public string errorMessage;
    public List<string> migrationPath;

    public SaveVersionCheckResult()
    {
        migrationPath = new List<string>();
    }
}

/// <summary>
/// 迁移结果
/// </summary>
[System.Serializable]
public class SaveMigrationResult
{
    public bool success;
    public string originalVersion;
    public string targetVersion;
    public SaveData originalData;
    public SaveData migratedData;
    public List<string> migrationSteps;
    public bool backupCreated;
    public string errorMessage;

    public SaveMigrationResult()
    {
        migrationSteps = new List<string>();
    }
}

/// <summary>
/// 恢复结果
/// </summary>
[System.Serializable]
public class SaveRecoveryResult
{
    public bool success;
    public string fileName;
    public SaveData recoveredData;
    public string recoveryMethod;
    public int attempts;
    public bool isPartialRecovery;
    public string errorMessage;
}

/// <summary>
/// 备份信息
/// </summary>
[System.Serializable]
public class BackupInfo
{
    public string originalVersion;
    public string backupTime;
    public string backupReason;
    public string description;
}

/// <summary>
/// 存档操作类型
/// </summary>
public enum SaveLoadOperationType
{
    Save,
    Load,
    Delete,
    Backup,
    Migration,
    Recovery
}

/// <summary>
/// 存档操作记录
/// </summary>
[System.Serializable]
public class SaveLoadOperation
{
    public SaveLoadOperationType operationType;
    public string fileName;
    public bool success;
    public float duration;
    public long fileSize;
    public string errorMessage;
    public DateTime timestamp;
}

/// <summary>
/// 性能报告
/// </summary>
[System.Serializable]
public class PerformanceReport
{
    public int totalOperations;
    public int successfulOperations;
    public int failedOperations;
    public float successRate;
    public float averageSaveTime;
    public float averageLoadTime;
    public float averageFileSize;
    public float currentMemoryUsage;
    public List<SaveLoadOperation> recentOperations;

    public PerformanceReport()
    {
        recentOperations = new List<SaveLoadOperation>();
    }
}
