using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 听众数据服务
/// 负责处理听众相关的业务逻辑
/// </summary>
public class AudienceService : MonoBehaviour
{
    [Header("=== 听众数据 ===")]
    [Tooltip("所有听众数据")]
    public List<AudienceDataSO> allAudiences = new List<AudienceDataSO>();

    // 单例模式
    public static AudienceService Instance { get; private set; }

    #region Unity生命周期

    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    #endregion

    #region DTV系统

    /// <summary>
    /// 增加听众DTV
    /// </summary>
    public bool IncreaseDTV(AudienceDataSO audience, int amount = 1)
    {
        if (audience == null || amount <= 0) return false;

        int oldDTV = audience.currentDTV;
        audience.currentDTV = Mathf.Min(5, audience.currentDTV + amount);

        if (audience.currentDTV > oldDTV)
        {
            Debug.Log($"{audience.audienceName} DTV提升：{oldDTV} -> {audience.currentDTV}");

            // 检查是否解锁新故事块
            CheckStoryBlockUnlocks(audience);

            // 更新听众状态
            UpdateAudienceState(audience);

            return true;
        }

        return false;
    }

    /// <summary>
    /// 检查是否可以解锁指定等级的故事块
    /// </summary>
    public bool CanUnlockStoryBlock(AudienceDataSO audience, int level)
    {
        if (audience == null || level < 1 || level > 6) return false;

        // 检查DTV要求
        if (audience.currentDTV < level) return false;

        // 检查是否已经解锁
        if (audience.unlockedStoryBlocks[level - 1]) return false;

        // 检查故事块是否存在
        var storyBlock = GetStoryBlock(audience, level);
        if (storyBlock == null) return false;

        // 检查其他解锁条件（由StoryService处理）
        return StoryService.Instance?.CanUnlockStoryBlock(storyBlock, audience) ?? false;
    }

    /// <summary>
    /// 解锁故事块
    /// </summary>
    public bool UnlockStoryBlock(AudienceDataSO audience, int level)
    {
        if (!CanUnlockStoryBlock(audience, level)) return false;

        audience.unlockedStoryBlocks[level - 1] = true;
        audience.LastInteractionTime = DateTime.Now;

        var storyBlock = GetStoryBlock(audience, level);
        Debug.Log($"{audience.audienceName} 解锁故事块 Level {level}：{storyBlock?.title}");

        return true;
    }

    /// <summary>
    /// 检查故事块解锁
    /// </summary>
    private void CheckStoryBlockUnlocks(AudienceDataSO audience)
    {
        for (int level = 1; level <= audience.currentDTV; level++)
        {
            if (!audience.unlockedStoryBlocks[level - 1])
            {
                if (CanUnlockStoryBlock(audience, level))
                {
                    UnlockStoryBlock(audience, level);
                }
            }
        }
    }

    #endregion

    #region 关键词管理

    /// <summary>
    /// 收集关键词
    /// </summary>
    public bool CollectKeyword(AudienceDataSO audience, KeywordDataSO keyword)
    {
        if (audience == null || keyword == null) return false;

        // 检查是否已经拥有
        if (HasKeyword(audience, keyword.keyword)) return false;

        // 初始化关键词
        var keywordInstance = Instantiate(keyword);
        KeywordService.Instance?.InitializeKeyword(keywordInstance);
        keywordInstance.associatedAudienceId = audience.audienceId;

        audience.collectedKeywords.Add(keywordInstance);
        audience.LastInteractionTime = DateTime.Now;

        Debug.Log($"{audience.audienceName} 收集关键词：{keyword.keyword}");
        return true;
    }

    /// <summary>
    /// 检查是否拥有指定关键词
    /// </summary>
    public bool HasKeyword(AudienceDataSO audience, string keywordText)
    {
        if (audience == null) return false;

        return audience.collectedKeywords.Any(k =>
            k != null &&
            k.keyword == keywordText &&
            !KeywordService.Instance.IsKeywordExpired(k));
    }

    /// <summary>
    /// 获取有效的关键词列表
    /// </summary>
    public List<KeywordDataSO> GetValidKeywords(AudienceDataSO audience)
    {
        if (audience == null) return new List<KeywordDataSO>();

        return audience.collectedKeywords.Where(k =>
            k != null &&
            !KeywordService.Instance.IsKeywordExpired(k)).ToList();
    }

    /// <summary>
    /// 清理过期关键词
    /// </summary>
    public void CleanupExpiredKeywords(AudienceDataSO audience)
    {
        if (audience == null) return;

        var expired = audience.collectedKeywords.Where(k =>
            k != null &&
            KeywordService.Instance.IsKeywordExpired(k)).ToList();

        foreach (var expiredKeyword in expired)
        {
            audience.collectedKeywords.Remove(expiredKeyword);
            audience.expiredKeywords.Add(expiredKeyword);
            Debug.Log($"{audience.audienceName} 关键词过期：{expiredKeyword.keyword}");
        }
    }

    #endregion

    #region 听众状态管理

    /// <summary>
    /// 更新听众状态
    /// </summary>
    public void UpdateAudienceState(AudienceDataSO audience)
    {
        if (audience == null) return;

        AudienceState newState = CalculateAudienceState(audience);

        if (newState != audience.currentState)
        {
            AudienceState oldState = audience.currentState;
            audience.currentState = newState;

            Debug.Log($"{audience.audienceName} 状态变化：{oldState} -> {newState}");
        }
    }

    /// <summary>
    /// 计算听众状态
    /// </summary>
    private AudienceState CalculateAudienceState(AudienceDataSO audience)
    {
        if (audience.currentDTV == 0) return AudienceState.Unknown;
        if (audience.currentDTV >= 5) return AudienceState.Completed;
        if (audience.currentDTV >= 4) return AudienceState.Intimate;
        if (audience.currentDTV >= 3) return AudienceState.Trusted;
        if (audience.currentDTV >= 2) return AudienceState.Contacted;
        if (audience.currentDTV >= 1) return AudienceState.Discovered;

        return AudienceState.Unknown;
    }

    /// <summary>
    /// 记录互动
    /// </summary>
    public void RecordInteraction(AudienceDataSO audience, bool isSuccessful, bool isResonance = false)
    {
        if (audience == null) return;

        audience.totalInteractions++;
        audience.LastInteractionTime = DateTime.Now;

        if (isSuccessful && isResonance)
        {
            audience.successfulResonances++;
        }
        else if (!isSuccessful)
        {
            audience.failedInquiries++;
        }

        Debug.Log($"{audience.audienceName} 互动记录：总计{audience.totalInteractions}次，成功共鸣{audience.successfulResonances}次");
    }

    #endregion

    #region 画像系统

    /// <summary>
    /// 获取当前头像
    /// </summary>
    public Sprite GetCurrentPortrait(AudienceDataSO audience)
    {
        if (audience == null) return null;

        float completeness = GetProfileCompleteness(audience);
        return completeness > 0.7f ? audience.portraitClear : audience.portraitBlurred;
    }

    /// <summary>
    /// 获取画像完整度
    /// </summary>
    public float GetProfileCompleteness(AudienceDataSO audience)
    {
        if (audience == null) return 0f;

        int totalKeywords = GetTotalKeywordsCount(audience);
        if (totalKeywords == 0) return 0f;

        int collectedCount = GetValidKeywords(audience).Count;
        return (float)collectedCount / totalKeywords;
    }

    /// <summary>
    /// 获取总关键词数量
    /// </summary>
    private int GetTotalKeywordsCount(AudienceDataSO audience)
    {
        if (audience == null) return 0;

        // 计算所有故事块中的关键词总数
        int total = 0;
        foreach (var storyBlock in audience.storyBlocks)
        {
            if (storyBlock != null)
            {
                total += storyBlock.rewardKeywords.Length;
            }
        }
        return total;
    }

    #endregion

    #region 故事块管理

    /// <summary>
    /// 获取故事块
    /// </summary>
    public StoryBlockSO GetStoryBlock(AudienceDataSO audience, int level)
    {
        if (audience == null || level < 1 || level > 6) return null;

        return audience.storyBlocks.FirstOrDefault(sb => sb != null && sb.level == level);
    }

    /// <summary>
    /// 获取已解锁的故事块
    /// </summary>
    public List<StoryBlockSO> GetUnlockedStoryBlocks(AudienceDataSO audience)
    {
        if (audience == null) return new List<StoryBlockSO>();

        List<StoryBlockSO> unlockedBlocks = new List<StoryBlockSO>();

        for (int i = 0; i < audience.unlockedStoryBlocks.Length; i++)
        {
            if (audience.unlockedStoryBlocks[i])
            {
                var storyBlock = GetStoryBlock(audience, i + 1);
                if (storyBlock != null)
                {
                    unlockedBlocks.Add(storyBlock);
                }
            }
        }

        return unlockedBlocks;
    }

    #endregion

    #region 听众查找

    /// <summary>
    /// 根据ID查找听众
    /// </summary>
    public AudienceDataSO FindAudienceById(string audienceId)
    {
        return allAudiences.FirstOrDefault(a => a != null && a.audienceId == audienceId);
    }

    /// <summary>
    /// 根据名称查找听众
    /// </summary>
    public AudienceDataSO FindAudienceByName(string audienceName)
    {
        return allAudiences.FirstOrDefault(a => a != null && a.audienceName == audienceName);
    }

    /// <summary>
    /// 获取活跃听众列表
    /// </summary>
    public List<AudienceDataSO> GetActiveAudiences()
    {
        return allAudiences.Where(a =>
            a != null &&
            a.currentState != AudienceState.Unknown).ToList();
    }

    /// <summary>
    /// 获取可互动的听众列表
    /// </summary>
    public List<AudienceDataSO> GetInteractableAudiences()
    {
        return allAudiences.Where(a =>
            a != null &&
            a.currentDTV < 5 &&
            a.currentState != AudienceState.Unknown).ToList();
    }

    #endregion

    #region 数据验证

    /// <summary>
    /// 验证听众数据
    /// </summary>
    public bool ValidateAudienceData(AudienceDataSO audience)
    {
        if (audience == null) return false;

        bool isValid = true;

        // 检查基础数据
        if (string.IsNullOrEmpty(audience.audienceId))
        {
            Debug.LogWarning($"听众 {audience.audienceName} 缺少ID");
            isValid = false;
        }

        // 检查DTV范围
        if (audience.currentDTV < 0 || audience.currentDTV > 5)
        {
            Debug.LogWarning($"听众 {audience.audienceName} DTV异常：{audience.currentDTV}");
            audience.currentDTV = Mathf.Clamp(audience.currentDTV, 0, 5);
            isValid = false;
        }

        // 检查故事块数组
        if (audience.unlockedStoryBlocks.Length != 6)
        {
            Debug.LogWarning($"听众 {audience.audienceName} 故事块数组长度异常");
            Array.Resize(ref audience.unlockedStoryBlocks, 6);
            isValid = false;
        }

        // 清理空引用
        audience.collectedKeywords.RemoveAll(k => k == null);
        audience.expiredKeywords.RemoveAll(k => k == null);
        audience.storyBlocks.RemoveAll(sb => sb == null);

        return isValid;
    }

    /// <summary>
    /// 验证所有听众数据
    /// </summary>
    public void ValidateAllAudienceData()
    {
        foreach (var audience in allAudiences)
        {
            ValidateAudienceData(audience);
        }
    }

    #endregion
}
